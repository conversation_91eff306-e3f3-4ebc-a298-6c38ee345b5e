package com.bukuwarung.edc.webview

import com.bukuwarung.edc.global.network.model.request.SessionRequest
import com.bukuwarung.edc.global.network.model.response.SessionResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

interface WebviewDataSource {

    @POST("/api/v2/auth/users/bacon")
    suspend fun createNewSession(@Body sessionRequest: SessionRequest): Response<SessionResponse>
}