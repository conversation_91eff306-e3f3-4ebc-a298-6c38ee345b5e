package com.bukuwarung.edc.util

import android.content.Context
import android.graphics.Bitmap
import android.media.ThumbnailUtils
import android.os.Build
import android.provider.MediaStore
import android.util.Size
import androidx.core.net.toUri
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Video utility extensions for thumbnail generation and video operations
 */

/**
 * Generates a video thumbnail from a video path (content URI or file path)
 * 
 * @param context The context for content resolver access
 * @return Bitmap thumbnail or null if generation fails
 */
suspend fun String.generateVideoThumbnail(context: Context): Bitmap? = withContext(Dispatchers.IO) {
    try {
        if (startsWith("content://")) {
            generateThumbnailFromContentUri(context)
        } else {
            generateThumbnailFromFilePath()
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * Generates thumbnail from content URI (content://)
 */
private fun String.generateThumbnailFromContentUri(context: Context): Bitmap? {
    val uri = toUri()
    
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        context.contentResolver.loadThumbnail(uri, Size(300, 200), null)
    } else {
        val cursor = context.contentResolver.query(
            uri, arrayOf(MediaStore.Video.Media.DATA), null, null, null
        )
        cursor?.use {
            if (it.moveToFirst()) {
                val filePath = it.getString(it.getColumnIndexOrThrow(MediaStore.Video.Media.DATA))
                filePath.generateThumbnailFromFilePath()
            } else null
        }
    }
}

/**
 * Generates thumbnail from file path
 */
private fun String.generateThumbnailFromFilePath(): Bitmap? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        ThumbnailUtils.createVideoThumbnail(File(this), Size(300, 200), null)
    } else {
        @Suppress("DEPRECATION")
        ThumbnailUtils.createVideoThumbnail(this, MediaStore.Video.Thumbnails.MINI_KIND)
    }
}
