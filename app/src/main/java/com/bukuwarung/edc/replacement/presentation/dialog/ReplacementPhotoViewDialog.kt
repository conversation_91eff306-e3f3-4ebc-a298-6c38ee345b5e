package com.bukuwarung.edc.replacement.presentation.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogReplacementPhotoViewBinding

class ReplacementPhotoViewDialog : DialogFragment() {

    private var _binding: DialogReplacementPhotoViewBinding? = null
    private val binding get() = _binding!!
    
    private var photoPath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogStyle)
        
        arguments?.let {
            photoPath = it.getString(ARG_PHOTO_PATH)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogReplacementPhotoViewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupDialog()
        setupPhotoDisplay()
    }

    private fun setupDialog() {
        binding.btnClose.setOnClickListener {
            dismiss()
        }

        // Make dialog fill most of the screen width
        dialog?.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupPhotoDisplay() {
        val path = photoPath ?: return
        
        try {
            binding.pbLoading.visibility = View.VISIBLE

            Glide.with(this)
                .load(path)
                .placeholder(R.drawable.replacement_ic_image_placeholder)
                .error(R.drawable.replacement_ic_image_placeholder)
                .transition(DrawableTransitionOptions.withCrossFade())
                .listener(object : com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable> {
                    override fun onLoadFailed(
                        e: com.bumptech.glide.load.engine.GlideException?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                        isFirstResource: Boolean
                    ): Boolean {
                        binding.pbLoading.visibility = View.GONE
                        return false
                    }

                    override fun onResourceReady(
                        resource: android.graphics.drawable.Drawable?,
                        model: Any?,
                        target: com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable>?,
                        dataSource: com.bumptech.glide.load.DataSource?,
                        isFirstResource: Boolean
                    ): Boolean {
                        binding.pbLoading.visibility = View.GONE
                        
                        // Apply dynamic sizing similar to video dialog
                        resource?.let { drawable ->
                            applyDynamicImageSizing(drawable)
                        }
                        
                        return false
                    }
                })
                .into(binding.ivPhoto)
            
        } catch (e: Exception) {
            binding.pbLoading.visibility = View.GONE
            binding.ivPhoto.setImageResource(R.drawable.replacement_ic_image_placeholder)
        }
    }

    private fun applyDynamicImageSizing(drawable: android.graphics.drawable.Drawable) {
        val imageWidth = drawable.intrinsicWidth
        val imageHeight = drawable.intrinsicHeight

        android.util.Log.d("PhotoDialog", "=== DEBUG SIZING ===")
        android.util.Log.d("PhotoDialog", "Image dimensions: ${imageWidth} x ${imageHeight}")

        if (imageWidth > 0 && imageHeight > 0) {
            val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
            android.util.Log.d("PhotoDialog", "Aspect ratio: $aspectRatio")
            
            val screenWidth = (resources.displayMetrics.widthPixels * 0.9).toInt()
            val maxHeight = (resources.displayMetrics.heightPixels * 0.6).toInt()
            android.util.Log.d("PhotoDialog", "Screen width (90%): $screenWidth")
            android.util.Log.d("PhotoDialog", "Max height (60%): $maxHeight")

            val calculatedHeight = (screenWidth / aspectRatio).toInt()
            android.util.Log.d("PhotoDialog", "Calculated height: $calculatedHeight")
            
            val finalHeight = if (calculatedHeight > maxHeight) maxHeight else calculatedHeight
            android.util.Log.d("PhotoDialog", "Final height: $finalHeight")

            val finalWidth = screenWidth - (resources.displayMetrics.density * 32).toInt()
            android.util.Log.d("PhotoDialog", "Final width: $finalWidth")
            android.util.Log.d("PhotoDialog", "Density: ${resources.displayMetrics.density}")

            val layoutParams = binding.ivPhoto.layoutParams
            android.util.Log.d("PhotoDialog", "Original layout params: ${layoutParams.width} x ${layoutParams.height}")
            
            layoutParams.width = finalWidth
            layoutParams.height = finalHeight
            binding.ivPhoto.layoutParams = layoutParams
            
            android.util.Log.d("PhotoDialog", "Applied layout params: ${finalWidth} x ${finalHeight}")
            android.util.Log.d("PhotoDialog", "=== END DEBUG ===")
        } else {
            android.util.Log.d("PhotoDialog", "Invalid image dimensions: ${imageWidth} x ${imageHeight}")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_PHOTO_PATH = "photo_path"

        fun newInstance(photoPath: String): ReplacementPhotoViewDialog {
            return ReplacementPhotoViewDialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_PHOTO_PATH, photoPath)
                }
            }
        }
    }
}
