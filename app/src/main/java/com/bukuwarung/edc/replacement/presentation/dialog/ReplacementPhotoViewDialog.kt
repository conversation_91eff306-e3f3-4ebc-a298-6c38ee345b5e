package com.bukuwarung.edc.replacement.presentation.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogReplacementPhotoViewBinding

class ReplacementPhotoViewDialog : DialogFragment() {

    private var _binding: DialogReplacementPhotoViewBinding? = null
    private val binding get() = _binding!!
    
    private var photoPath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogStyle)
        
        arguments?.let {
            photoPath = it.getString(ARG_PHOTO_PATH)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogReplacementPhotoViewBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupDialog()
        setupPhotoDisplay()
    }

    private fun setupDialog() {
        binding.btnClose.setOnClickListener {
            dismiss()
        }

        // Make dialog fill most of the screen width like video dialog
        dialog?.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupPhotoDisplay() {
        val path = photoPath ?: return

        try {
            binding.pbLoading.visibility = View.VISIBLE

            Glide.with(this)
                .asBitmap()
                .load(path)
                .placeholder(R.drawable.replacement_ic_image_placeholder)
                .error(R.drawable.replacement_ic_image_placeholder)
                .into(object : com.bumptech.glide.request.target.CustomTarget<android.graphics.Bitmap>() {
                    override fun onResourceReady(
                        resource: android.graphics.Bitmap,
                        transition: com.bumptech.glide.request.transition.Transition<in android.graphics.Bitmap>?
                    ) {
                        binding.pbLoading.visibility = View.GONE

                        // Calculate dimensions using normal aspect ratio scaling
                        val imageWidth = resource.width
                        val imageHeight = resource.height

                        if (imageWidth > 0 && imageHeight > 0) {
                            val aspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
                            val screenWidth = (resources.displayMetrics.widthPixels * 0.9).toInt()
                            val availableWidth = screenWidth - (resources.displayMetrics.density * 32).toInt()
                            val maxHeight = (resources.displayMetrics.heightPixels * 0.6).toInt()

                            val calculatedHeight = (availableWidth / aspectRatio).toInt()
                            val finalHeight = if (calculatedHeight > maxHeight) maxHeight else calculatedHeight

                            val layoutParams = binding.ivPhoto.layoutParams
                            layoutParams.width = availableWidth
                            layoutParams.height = finalHeight
                            binding.ivPhoto.layoutParams = layoutParams
                            binding.ivPhoto.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
                        }

                        binding.ivPhoto.setImageBitmap(resource)
                    }

                    override fun onLoadCleared(placeholder: android.graphics.drawable.Drawable?) {
                        binding.pbLoading.visibility = View.GONE
                    }

                    override fun onLoadFailed(errorDrawable: android.graphics.drawable.Drawable?) {
                        binding.pbLoading.visibility = View.GONE
                        binding.ivPhoto.setImageResource(R.drawable.replacement_ic_image_placeholder)
                    }
                })

        } catch (e: Exception) {
            binding.pbLoading.visibility = View.GONE
            binding.ivPhoto.setImageResource(R.drawable.replacement_ic_image_placeholder)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    companion object {
        private const val ARG_PHOTO_PATH = "photo_path"

        fun newInstance(photoPath: String): ReplacementPhotoViewDialog {
            return ReplacementPhotoViewDialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_PHOTO_PATH, photoPath)
                }
            }
        }
    }
}
