package com.bukuwarung.edc.replacement.data.model

import com.google.gson.annotations.SerializedName

data class ReplacementReasonResponse(
    @SerializedName("result")
    val result: <PERSON><PERSON><PERSON>,
    @SerializedName("data")
    val data: List<ReplacementReasonDto>
)

data class ReplacementReasonDto(
    @SerializedName("label")
    val label: String,
    @SerializedName("imageMandatory")
    val imageMandatory: <PERSON><PERSON><PERSON>,
    @SerializedName("videoMandatory")
    val videoMandatory: Bo<PERSON>an
)
