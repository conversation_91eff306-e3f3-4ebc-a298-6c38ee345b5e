package com.bukuwarung.edc.replacement.presentation.form

import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.activity.addCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityReplacementFormBinding
import com.bukuwarung.edc.replacement.presentation.dialog.ReplacementVideoPlaybackDialog
import com.bukuwarung.edc.replacement.presentation.dialog.ReplacementPhotoViewDialog
import com.bukuwarung.edc.util.generateVideoThumbnail
import com.bumptech.glide.Glide
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ReplacementFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityReplacementFormBinding

    private val viewModel: ReplacementFormViewModel by viewModels()
    private val reasonAdapter: ReplacementFormReasonAdapter by lazy {
        ReplacementFormReasonAdapter(callback = provideCallback())
    }

    private val videoFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? -> handleMediaSelection(uri) { path -> viewModel.setUploadedVideo(path) } }

    private val photoFilePickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? -> handleMediaSelection(uri) { path -> viewModel.setUploadedPhoto(path) } }

    private fun handleMediaSelection(uri: Uri?, onSuccess: (String) -> Unit) {
        uri?.let { mediaUri ->
            val mediaPath = getFilePathFromUri(mediaUri) ?: mediaUri.toString()
            onSuccess(mediaPath)
        }
    }

    private val colorDefault by lazy {
        ContextCompat.getColor(this, R.color.black_800)
    }

    private val colorRed by lazy {
        ContextCompat.getColor(this, android.R.color.holo_red_dark)
    }

    private val minChars by lazy {
        ReplacementFormViewModel.OTHER_REASON_MIN_CHARS
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReplacementFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupBackPressedHandler()
        setupView()
        observeUiState()
    }

    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this) { finish() }
    }

    private fun setupView() {
        binding.apply {
            ivBack.setOnClickListener { onBackPressedDispatcher.onBackPressed() }

            rvReplacementReasons.apply {
                adapter = reasonAdapter
                setHasFixedSize(true)
                isNestedScrollingEnabled = false
            }

            etOtherReason.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    viewModel.updateOtherReasonText(s?.toString() ?: "")
                }
            })

            tvUploadVideoDevice.setOnClickListener {
                handleVideoUpload()
            }

            ivUploadedVideoDelete.setOnClickListener {
                viewModel.removeUploadedVideo()
            }

            tvUploadPhotoDevice.setOnClickListener {
                handlePhotoUpload()
            }

            ivUploadedPhotoDelete.setOnClickListener {
                viewModel.removeUploadedPhoto()
            }

            btnNext.setOnClickListener { viewModel.submitForm() }
        }
        loadReplacementReasons()
    }

    private fun handleVideoUpload() {
        videoFilePickerLauncher.launch("video/*")
    }

    private fun handlePhotoUpload() {
        photoFilePickerLauncher.launch("image/*")
    }

    private fun loadReplacementReasons() {
        viewModel.loadReplacementReasons()
    }

    private fun observeUiState() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUi(state)
            }
        }
    }

    private fun updateUi(state: ReplacementFormUiState) {
        binding.apply {
            tvDeviceTitle.text = state.deviceInfo.name
            tvDeviceTerminalNumber.text = state.deviceInfo.terminalNumber
            tvDeviceSerialNumber.text = state.deviceInfo.serialNumber
            tvUserIdNumber.text = state.deviceInfo.userId
        }

        val reasonsWithSelection = state.replacementReasons.map { reason ->
            reason.copy(isSelected = reason.id in state.selectedReasons)
        }
        reasonAdapter.submitList(reasonsWithSelection)

        updateSubmitButton(state.isSubmitButtonEnabled)
        updateUploadViews(state)

        updateMediaRequirementIndicator(state)
        updateOtherReasonViews(state)

        state.errorMessage?.let { error ->
            Toast.makeText(this@ReplacementFormActivity, error, Toast.LENGTH_LONG).show()
        }
    }

    private fun updateOtherReasonViews(state: ReplacementFormUiState) {
        binding.apply {
            tilOtherReason.visibility = if (state.isOtherReasonVisible) View.VISIBLE else View.GONE
            tvOtherReasonLabel.visibility = if (state.isOtherReasonVisible) View.VISIBLE else View.GONE

            if (state.isOtherReasonVisible) {
                val textLength = state.otherReasonText.length
                tilOtherReason.helperText = if (textLength < minChars) {
                    getString(R.string.replacement_other_reason_min_chars_helper, minChars, textLength, minChars)
                } else {
                    null
                }

                tilOtherReason.error = if (textLength in 1 until minChars) {
                    getString(R.string.replacement_other_reason_min_chars_error, minChars)
                } else {
                    null
                }

                tvOtherReasonLabel.setTextColor(
                    if (state.otherReasonText.length >= minChars) colorDefault else colorRed
                )
            } else {
                tvOtherReasonLabel.setTextColor(colorDefault)
                etOtherReason.text = null
            }
        }
    }

    private fun updateMediaRequirementIndicator(state: ReplacementFormUiState) {
        binding.apply {
            val videoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_video_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_video_label)
            }

            val photoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_photo_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_photo_label)
            }
            
            tvUploadVideoDeviceLabel.text = videoLabel
            tvUploadPhotoDeviceLabel.text = photoLabel

            if (state.isMediaRequired) {
                tvUploadVideoDeviceLabel.setTextColor(
                    if (state.uploadedVideoPath == null) colorRed else colorDefault
                )
                tvUploadPhotoDeviceLabel.setTextColor(
                    if (state.uploadedPhotoPath == null) colorRed else colorDefault
                )
            } else {
                tvUploadVideoDeviceLabel.setTextColor(colorDefault)
                tvUploadPhotoDeviceLabel.setTextColor(colorDefault)
            }
        }
    }

    private fun updateSubmitButton(isEnabled: Boolean) {
        binding.btnNext.apply {
            this.isEnabled = isEnabled
            backgroundTintList = ContextCompat.getColorStateList(
                context,
                if (isEnabled) R.color.bar_dashboard_ppob_3
                else R.color.switch_thumb_disabled_material_light
            )
            setTextColor(
                ContextCompat.getColor(
                    context,
                    if (isEnabled) R.color.black_800
                    else R.color.white
                )
            )
        }
    }

    private fun updateUploadViews(state: ReplacementFormUiState) {
        binding.apply {
            vfUploadVideoDevice.displayedChild = when {
                state.isUploadingVideo -> CHILD_MEDIA_UPLOADING
                state.uploadedVideoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }
            if (state.uploadedVideoPath != null) {
                setupVideoPreview(state.uploadedVideoPath)
            }

            vfUploadPhotoDevice.displayedChild = when {
                state.isUploadingPhoto -> CHILD_MEDIA_UPLOADING
                state.uploadedPhotoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }
            if (state.uploadedPhotoPath != null) {
                setupPhotoPreview(state.uploadedPhotoPath)
            }
            tvUploadVideoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
            tvUploadPhotoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
        }
    }

    private fun provideCallback(): ReplacementFormReasonAdapter.Callback {
        return object : ReplacementFormReasonAdapter.Callback {
            override fun onItemToggle(reasonId: String) {
                viewModel.toggleReason(reasonId)
            }
        }
    }

    private fun setupVideoPreview(videoPath: String) {
        binding.apply {
            lifecycleScope.launch {
                val thumbnail = videoPath.generateVideoThumbnail(this@ReplacementFormActivity)
                displayVideoThumbnail(thumbnail)
                setupVideoClickListeners(videoPath)
            }
        }
    }

    private fun displayVideoThumbnail(thumbnail: Bitmap?) {
        binding.apply {
            if (thumbnail != null) {
                ivUploadedVideoContent.setImageBitmap(thumbnail)
                ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
            } else {
                displayVideoPlaceholder()
            }
        }
    }

    private fun displayVideoPlaceholder() {
        binding.apply {
            ivUploadedVideoContent.setImageResource(R.drawable.replacement_ic_video_file)
            ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_INSIDE
        }
    }

    private fun setupVideoClickListeners(videoPath: String) {
        binding.apply {
            ivUploadedVideoDelete.setOnClickListener {
                viewModel.removeUploadedVideo()
            }
            cvVideoThumbnail.setOnClickListener {
                showVideoPlaybackDialog(videoPath)
            }
        }
    }

    private fun showVideoPlaybackDialog(videoPath: String) {
        val dialog = ReplacementVideoPlaybackDialog.newInstance(videoPath)
        dialog.show(supportFragmentManager, "VideoPlaybackDialog")
    }

    private fun setupPhotoPreview(photoPath: String) {
        binding.apply {
            try {
                Glide.with(this@ReplacementFormActivity)
                    .load(photoPath)
                    .placeholder(R.drawable.replacement_ic_image_placeholder)
                    .error(R.drawable.replacement_ic_image_placeholder)
                    .centerCrop()
                    .into(ivUploadedPhotoContent)

                setupPhotoClickListeners(photoPath)
            } catch (e: Exception) {
                displayPhotoPlaceholder()
                setupPhotoClickListeners(photoPath)
            }
        }
    }

    private fun displayPhotoPlaceholder() {
        binding.apply {
            ivUploadedPhotoContent.setImageResource(R.drawable.replacement_ic_image_placeholder)
            ivUploadedPhotoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
        }
    }

    private fun setupPhotoClickListeners(photoPath: String) {
        binding.apply {
            ivUploadedPhotoDelete.setOnClickListener {
                viewModel.removeUploadedPhoto()
            }

            // Add click listener for fullscreen dialog
            cvPhotoThumbnail.setOnClickListener {
                showPhotoViewDialog(photoPath)
            }
        }
    }

    private fun showPhotoViewDialog(photoPath: String) {
        val dialog = ReplacementPhotoViewDialog.newInstance(photoPath)
        dialog.show(supportFragmentManager, "PhotoViewDialog")
    }

    private fun getFilePathFromUri(uri: Uri): String? {
        return try {
            uri.toString()
        } catch (e: Exception) {
            null
        }
    }

    companion object {
        private const val CHILD_UPLOAD_MEDIA = 0
        private const val CHILD_MEDIA_UPLOADED = 1
        private const val CHILD_MEDIA_UPLOADING = 2
        private const val ASTERISK = "*"
    }

    object IntentExtras {
        const val DEVICE_NAME = "extra_device_name"
        const val DEVICE_TYPE = "extra_device_type"
        const val DEVICE_TERMINAL_NUMBER = "extra_device_terminal_number"
        const val DEVICE_SERIAL_NUMBER = "extra_device_serial_number"
        const val USER_ID = "extra_user_id"
    }
}

typealias ReplacementFormIntentExtras = ReplacementFormActivity.IntentExtras