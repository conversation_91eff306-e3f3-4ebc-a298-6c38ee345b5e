package com.bukuwarung.edc.replacement.presentation.form

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceCategory
import com.bukuwarung.edc.replacement.domain.usecase.GetReplacementReasonsUseCase
import com.bukuwarung.edc.replacement.domain.usecase.UploadReplacementEvidenceUseCase
import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ReplacementFormViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val getReplacementReasonsUseCase: GetReplacementReasonsUseCase,
    private val uploadReplacementEvidenceUseCase: UploadReplacementEvidenceUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(
        ReplacementFormUiState(deviceInfo = savedStateHandle.toDeviceInfo())
    )
    val uiState: StateFlow<ReplacementFormUiState> = _uiState.asStateFlow()

    companion object {
        private const val OTHER_REASON_LABEL = "Lainnya"
        const val OTHER_REASON_MIN_CHARS = 10
    }

    fun loadReplacementReasons() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            getReplacementReasonsUseCase(_uiState.value.deviceInfo.type)
                .onSuccess { reasons ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            replacementReasons = reasons,
                            isLoading = false,
                            errorMessage = null
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            isLoading = false,
                            errorMessage = error.message ?: "Failed to load replacement reasons"
                        )
                    }
                }
        }
    }

    fun toggleReason(reasonId: String) {
        _uiState.update { currentState ->
            val selectedReasons = currentState.selectedReasons.toMutableSet()
            val reason = currentState.replacementReasons.find { it.id == reasonId }

            if (reasonId in selectedReasons) {
                selectedReasons.remove(reasonId)
            } else {
                selectedReasons.add(reasonId)
            }

            val isMediaRequired = calculateMediaRequired(selectedReasons, currentState.replacementReasons)
            val isOtherReasonVisible = reason?.label == OTHER_REASON_LABEL

            currentState.copy(
                selectedReasons = selectedReasons,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    selectedReasons,
                    currentState.replacementReasons,
                    currentState.uploadedVideoPath,
                    currentState.uploadedPhotoPath,
                    currentState.otherReasonText
                ),
                isMediaRequired = isMediaRequired,
                isOtherReasonVisible = isOtherReasonVisible,
                otherReasonText = if (isOtherReasonVisible) currentState.otherReasonText else ""
            )
        }
    }

    fun updateOtherReasonText(text: String) {
        _uiState.update { currentState ->
            currentState.copy(
                otherReasonText = text,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    currentState.uploadedVideoPath,
                    currentState.uploadedPhotoPath,
                    text
                )
            )
        }
    }

    fun setUploadedVideo(videoPath: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isUploadingVideo = true) }
            
            uploadReplacementEvidenceUseCase(videoPath, ReplacementEvidenceCategory.VIDEO)
                .onSuccess { response ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            uploadedVideoPath = videoPath,
                            uploadedVideoUrl = response.signedUrl,
                            uploadedVideoServerPath = response.imagePath,
                            isUploadingVideo = false,
                            isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                                currentState.selectedReasons,
                                currentState.replacementReasons,
                                videoPath,
                                currentState.uploadedPhotoPath,
                                currentState.otherReasonText
                            )
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingVideo = false,
                            errorMessage = "Failed to upload video: ${error.message}"
                        )
                    }
                }
        }
    }

    fun setUploadedPhoto(photoPath: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isUploadingPhoto = true) }
            
            uploadReplacementEvidenceUseCase(photoPath, ReplacementEvidenceCategory.PHOTO)
                .onSuccess { response ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            uploadedPhotoPath = photoPath,
                            uploadedPhotoUrl = response.signedUrl,
                            uploadedPhotoServerPath = response.imagePath,
                            isUploadingPhoto = false,
                            isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                                currentState.selectedReasons,
                                currentState.replacementReasons,
                                currentState.uploadedVideoPath,
                                photoPath,
                                currentState.otherReasonText
                            )
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { currentState ->
                        currentState.copy(
                            isUploadingPhoto = false,
                            errorMessage = "Failed to upload photo: ${error.message}"
                        )
                    }
                }
        }
    }

    fun removeUploadedVideo() {
        _uiState.update { currentState ->
            currentState.copy(
                uploadedVideoPath = null,
                uploadedVideoUrl = null,
                uploadedVideoServerPath = null,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    null,
                    currentState.uploadedPhotoPath,
                    currentState.otherReasonText
                )
            )
        }
    }

    fun removeUploadedPhoto() {
        _uiState.update { currentState ->
            currentState.copy(
                uploadedPhotoPath = null,
                uploadedPhotoUrl = null,
                uploadedPhotoServerPath = null,
                isSubmitButtonEnabled = calculateSubmitButtonEnabled(
                    currentState.selectedReasons,
                    currentState.replacementReasons,
                    currentState.uploadedVideoPath,
                    null,
                    currentState.otherReasonText
                )
            )
        }
    }

    fun submitForm() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }

            try {
                // TODO: Implement form submission
                _uiState.update { it.copy(isLoading = false) }
            } catch (e: Exception) {
                _uiState.update { currentState ->
                    currentState.copy(
                        errorMessage = e.message,
                        isLoading = false
                    )
                }
            }
        }
    }

    private fun calculateMediaRequired(
        selectedReasons: Set<String>, 
        allReasons: List<ReplacementFormReason>
    ): Boolean {
        return selectedReasons.any { reasonId ->
            val reason = allReasons.find { it.id == reasonId }
            reason?.imageMandatory == true || reason?.videoMandatory == true
        }
    }

    private fun calculateSubmitButtonEnabled(
        selectedReasons: Set<String>,
        allReasons: List<ReplacementFormReason>,
        videoPath: String?,
        photoPath: String?,
        otherReasonText: String? = null
    ): Boolean {
        if (selectedReasons.isEmpty()) return false

        val otherReasonSelected = selectedReasons.any { reasonId ->
            val reason = allReasons.find { it.id == reasonId }
            reason?.label == OTHER_REASON_LABEL
        }
        
        if (otherReasonSelected) {
            val text = otherReasonText ?: ""
            if (text.length < OTHER_REASON_MIN_CHARS) return false
        }

        val hasImageRequirement = selectedReasons.any { reasonId ->
            val reason = allReasons.find { it.id == reasonId }
            reason?.imageMandatory == true
        }
        
        val hasVideoRequirement = selectedReasons.any { reasonId ->
            val reason = allReasons.find { it.id == reasonId }
            reason?.videoMandatory == true
        }
        
        if (hasImageRequirement && photoPath == null) return false
        if (hasVideoRequirement && videoPath == null) return false
        
        return true
    }
}

fun SavedStateHandle.toDeviceInfo(): ReplacementFormDeviceInfo {
    return ReplacementFormDeviceInfo(
        name = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_NAME).orEmpty(),
        type = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_TYPE).toReplacementDeviceType(),
        terminalNumber = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_TERMINAL_NUMBER).orEmpty(),
        serialNumber = get<String>(ReplacementFormActivity.IntentExtras.DEVICE_SERIAL_NUMBER).orEmpty(),
        userId = get<String>(ReplacementFormActivity.IntentExtras.USER_ID).orEmpty()
    )
}
