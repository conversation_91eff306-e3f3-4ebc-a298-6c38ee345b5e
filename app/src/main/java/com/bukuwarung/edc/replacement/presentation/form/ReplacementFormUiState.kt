package com.bukuwarung.edc.replacement.presentation.form

import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason

data class ReplacementFormUiState(
    val isLoading: Boolean = false,
    val deviceInfo: ReplacementFormDeviceInfo = ReplacementFormDeviceInfo(),
    val replacementReasons: List<ReplacementFormReason> = emptyList(),
    val selectedReasons: Set<String> = emptySet(),
    val otherReasonText: String = "",
    val isOtherReasonVisible: Boolean = false,
    val uploadedVideoPath: String? = null,
    val uploadedVideoUrl: String? = null,
    val uploadedVideoServerPath: String? = null,
    val uploadedPhotoPath: String? = null,
    val uploadedPhotoUrl: String? = null,
    val uploadedPhotoServerPath: String? = null,
    val isUploadingVideo: Boolean = false,
    val isUploadingPhoto: Boolean = false,
    val isSubmitButtonEnabled: Boolean = false,
    val errorMessage: String? = null,
    val isMediaRequired: Boolean = false
)
