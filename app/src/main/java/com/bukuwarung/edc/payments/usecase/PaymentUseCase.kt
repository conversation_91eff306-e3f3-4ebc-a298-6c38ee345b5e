package com.bukuwarung.edc.payments.usecase

import com.bukuwarung.edc.global.network.ApiResponse
import com.bukuwarung.edc.global.network.ApiSuccessResponse
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.request.BankValidationRequest
import com.bukuwarung.edc.payments.data.model.request.DisbursementOverviewRequest
import com.bukuwarung.edc.payments.data.model.DisbursementRequest
import com.bukuwarung.edc.payments.data.model.PaymentHealthCheckRequest
import com.bukuwarung.edc.payments.data.model.UpdateFeeRequest
import com.bukuwarung.edc.payments.data.model.request.BankAccountRequest
import com.bukuwarung.edc.payments.data.repository.PaymentRepository
import retrofit2.Response
import javax.inject.Inject


class PaymentUseCase @Inject constructor(private val paymentRepository: PaymentRepository) {

    suspend fun getBanks() = paymentRepository.getBanks()

    suspend fun validateBankAccount(
        accountId: String,
        bankValidationRequest: BankValidationRequest
    ) = paymentRepository.validateBankAccount(accountId, bankValidationRequest)

    suspend fun getPaymentCategoryList(disbursableType: String) =
        paymentRepository.getPaymentCategoryList(disbursableType)

    suspend fun getPaymentOutLimits(accountId: String, customerId: String) =
        paymentRepository.getPaymentOutLimits(accountId, customerId)

    suspend fun doHealthCheck(paymentHealthCheckRequest: PaymentHealthCheckRequest) =
        paymentRepository.doHealthCheck(paymentHealthCheckRequest)

    suspend fun getDisbursementOverview(
        accountId: String,
        customerId: String,
        overviewRequest: DisbursementOverviewRequest
    ) = paymentRepository.getDisbursementOverview(accountId, customerId, overviewRequest)

    suspend fun validateAndAddCustomerBankAccount(
        accountId: String,
        customerId: String,
        bankValidationRequest: BankValidationRequest
    ) = paymentRepository.validateAndAddCustomerBankAccount(
        accountId,
        customerId,
        bankValidationRequest
    )

    suspend fun createDisbursement(
        accountId: String,
        customerId: String,
        request: DisbursementRequest
    ) = paymentRepository.createDisbursement(accountId, customerId, request)

    suspend fun updatePaymentOutAgentFee(
        businessId: String, customerId: String,
        paymentRequestId: String, updateFeeRequest: UpdateFeeRequest
    ) = paymentRepository.updatePaymentOutAgentFee(
        businessId, customerId, paymentRequestId, updateFeeRequest
    )

    suspend fun getPaymentMethods(
        accountId: String, paymentType: String, bankCode: String, transactionAmount: Double
    ) = paymentRepository.getPaymentMethods(accountId, paymentType, bankCode, transactionAmount)

    suspend fun getMerchantBankAccounts(
        businessId: String, type: String? = null
    ) = paymentRepository.getMerchantBankAccounts(businessId, type)

    suspend fun getCustomerBankAccounts(businessId: String, customerId: String): Response<List<BankAccount>> {
        val response = paymentRepository.getCustomerBankAccounts(businessId, customerId)
        return response
    }

    suspend fun deleteMerchantBankAccount(accountId: String, bankAccountId: String): Response<Any> =
        paymentRepository.deleteMerchantBankAccount(accountId, bankAccountId)

    suspend fun addMerchantBankAccount(
        accountId: String,
        bankAccount: BankAccountRequest
    ): Response<BankAccount> = paymentRepository.addMerchantBankAccount(accountId, bankAccount)
}