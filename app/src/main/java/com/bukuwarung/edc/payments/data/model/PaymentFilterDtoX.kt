package com.bukuwarung.edc.payments.data.model

import com.bukuwarung.edc.payments.constant.PaymentConst

data class PaymentFilterDtoX(
    var typeFilters: ArrayList<String> = arrayListOf(),
    var statusFilters: ArrayList<String> = arrayListOf(),
    var dateFilters: DateFilters = DateFilters(),
    var searchQuery: String = "",
    var sorting: String = "",
    var bukuOrigin: String? = null
)

data class DateFilters(
    var presetValue: PaymentConst.DATE_PRESET? = null,
    var startDate: Long? = null,
    var endDate: Long? = null
)

fun getDefaultFilters(type: PaymentConst.HISTORY_TABS): PaymentFilterDtoX {
    return when (type) {
        PaymentConst.HISTORY_TABS.ALL -> PaymentFilterDtoX()
        PaymentConst.HISTORY_TABS.PPOB -> PaymentFilterDtoX(arrayListOf(PaymentConst.TYPE_DIGITAL_PRODUCT))
        PaymentConst.HISTORY_TABS.PEMBAYARAN -> PaymentFilterDtoX(arrayListOf(PaymentConst.TYPE_PEMBAYARAN))
        PaymentConst.HISTORY_TABS.SALDO -> PaymentFilterDtoX(arrayListOf(PaymentConst.TYPE_SALDO_ALL))
    }
}