package com.bukuwarung.edc.card.transfermoney.viewmodel

import Resource
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.cardreader.CardReaderHelper
import com.bukuwarung.edc.card.data.model.PinCardErrorResponse
import com.bukuwarung.edc.card.domain.model.EdcTransactionResponse
import com.bukuwarung.edc.card.domain.usecase.CardTransferPostingUseCase
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.global.EdcApplication
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.util.Utils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransferMoneyViewModel @Inject constructor(
    private val cardTransferPostingUseCase: CardTransferPostingUseCase
) :
    ViewModel() {

    private val _transferMoney = MutableLiveData<Resource<Any>>()
    val transferMoney: LiveData<Resource<Any>> get() = _transferMoney

    fun transferMoney(accountId: String, transactionType: TransactionType, transferMoneyRequest: TransferMoneyRequestResponseBody) =
        viewModelScope.launch {
            try {
                _transferMoney.postValue(Resource.loading(null))
                cardTransferPostingUseCase.invoke(accountId, transactionType, transferMoneyRequest).collect { response ->
                    //transaction flow is completed, incomplete-txn data should be cleared
                    when (response) {
                        //Success = transfer posting API returned true and device EMV has verified backend response icc data
                        is EdcTransactionResponse.Success<*> -> {
                            Log.d("TRANSFER", "data $response.data")
                            if(Utils.isCardReader()) {
                                try {
                                    val status: Boolean =
                                        CardReaderHelper.getInstance()
                                            ?.confirmTransaction("Transaksi Berhasil") == true
                                    Log.d("card_reader_result", "Transaction Complete: $status")
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                            _transferMoney.postValue(Resource.success(response.data))
                        }
                        //check balance transaction failed = http failure or device validation faliure or backend rejected transaction
                        is EdcTransactionResponse.Failure -> {
                            Log.d("TRANSFER", "error $response")
                            if(Utils.isCardReader()) {
                                try {
                                    val status: Boolean =
                                        CardReaderHelper.getInstance()
                                            ?.confirmTransaction("Transaksi Gagal") == true
                                    Log.d("card_reader_result", "Transaction Failed: $status")
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                            _transferMoney.postValue(
                                Resource.error(
                                    response.message ?: "",
                                    PinCardErrorResponse(response.code, response.message ?: "",response.stan.orEmpty()
                                )
                            ))
                        }
                    }
                }
            } catch (e: NoConnectivityException) {
                handleServerError(e, ErrorMapping.noInternetErrorCode[0])
            } catch (e: Exception) {
                handleServerError(e, ErrorMapping.errorOccurredInSystemErrorCode[0])
            }
        }

    private fun handleServerError(e: Exception, code: String) {
        _transferMoney.postValue(
            Resource.error(
                e.message.toString(),
                PinCardErrorResponse(
                    code = code,
                    message = e.message.toString(),
                    ""
                )
            )
        )
    }

}