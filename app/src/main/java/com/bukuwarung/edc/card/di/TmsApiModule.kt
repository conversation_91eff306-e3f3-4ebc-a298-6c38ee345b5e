package com.bukuwarung.edc.card.di

import android.util.Log
import com.bukuwarung.edc.card.data.datasource.TerminalApi
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.network.TokenAuthenticator
import com.bukuwarung.edc.global.network.interceptors.HeadersInterceptor
import com.bukuwarung.edc.global.network.interceptors.NetworkConnectionInterceptor
import com.bukuwarung.edc.global.network.interceptors.SecuredInterceptor
import com.moczul.ok2curl.CurlInterceptor
import com.moczul.ok2curl.logger.Logger
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TmsApiModule {

    @Provides
    @Singleton
    fun provideRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.ACCOUNTING_API_BASE_URL+"/")
            .client(OkHttpClient.Builder()
                .addInterceptor(NetworkConnectionInterceptor())
                .addInterceptor(HeadersInterceptor())
                .addInterceptor(SecuredInterceptor())
                .authenticator(TokenAuthenticator())
                .addInterceptor(CurlInterceptor(object : Logger {
                    override fun log(message: String) {
                        Log.d("Ok2Curl", message)
                    }
                }))
                .addNetworkInterceptor(HttpLoggingInterceptor().setLevel(if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE))
                .build())
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    fun provideTerminalApi(retrofit: Retrofit): TerminalApi {
        return retrofit.create(TerminalApi::class.java)
    }
}
