package com.bukuwarung.edc.card.ui.edcdevices.usecase

import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceListResponse
import com.bukuwarung.edc.card.ui.edcdevices.repo.DeviceListRepository
import retrofit2.Response
import javax.inject.Inject

class DeviceListUseCase @Inject constructor(private val deviceListRepository: DeviceListRepository) {
    suspend fun getDeviceList(devicePlan: String):Response<DeviceListResponse> {
       return deviceListRepository.getDevice(devicePlan)
    }
}