<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_payment_method"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="56dp"
        android:layout_height="44dp"
        android:layout_marginVertical="@dimen/_16dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/ic_bank" />

    <TextView
        android:id="@+id/tv_bank_name"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_12dp"
        app:layout_constraintStart_toEndOf="@+id/iv_logo"
        app:layout_constraintTop_toTopOf="@+id/iv_logo"
        tools:text="Bank Central Asia (BCA)" />

    <TextView
        android:id="@+id/tv_new_label"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:background="@drawable/bg_solid_red60_corners_12dp"
        android:paddingHorizontal="@dimen/_8dp"
        android:paddingVertical="@dimen/_2dp"
        android:text=""
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="@+id/tv_bank_name"
        app:layout_constraintStart_toEndOf="@+id/tv_bank_name"
        app:layout_constraintTop_toTopOf="@+id/tv_bank_name" />

    <TextView
        android:id="@+id/tv_info"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@+id/barrier"
        app:layout_constraintStart_toStartOf="@+id/tv_bank_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_bank_name"
        tools:text="Rp0" />

    <TextView
        android:id="@+id/tv_error_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintEnd_toStartOf="@+id/barrier"
        app:layout_constraintStart_toStartOf="@+id/tv_bank_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_info"
        tools:text="Rp0" />

    <TextView
        android:id="@+id/tv_saldo_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:textColor="@color/red_60"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_2dp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/barrier"
        app:layout_constraintStart_toStartOf="@+id/tv_bank_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_error_info"
        tools:text="Kamu telah mencapai limit pemakaian bulanan" />

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/rb_select"
        style="@style/RadioButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginEnd="@dimen/_10dp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_logo" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_bnpl_not_registered"
        style="@style/ButtonOutline.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Ajukan" />

    <TextView
        android:id="@+id/tv_bnpl_in_progress"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:background="@color/yellow_5"
        android:drawablePadding="@dimen/_4dp"
        android:paddingHorizontal="@dimen/_8dp"
        android:paddingVertical="@dimen/_4dp"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_clock_in_progress"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Diproses" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="left"
        app:constraint_referenced_ids="btn_bnpl_not_registered, tv_bnpl_in_progress, rb_select" />

    <com.bukuwarung.ui_component.component.alert.BukuAlert
        android:id="@+id/ba_bank_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginTop="@dimen/_12dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_saldo_info" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="iv_logo, ba_bank_info"/>

    <com.bukuwarung.payments.widget.SaldoLimitsView
        android:id="@+id/saldo_limit_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@id/br_bottom" />

    <View
        android:id="@+id/vw_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/saldo_limit_view" />

    <include
        android:id="@+id/include_ppob_reward"
        layout="@layout/layout_reward_bottomsheet"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vw_line" />

</androidx.constraintlayout.widget.ConstraintLayout>