<?xml version="1.0" encoding="utf-8"?>
<HorizontalScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/hsv_category"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="6dp"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_all_categories"
            style="@style/PaymentFilterChoiceChipStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/other_categories"
            app:checkedIconVisible="false"
            app:chipIcon="@drawable/ic_list"
            app:chipIconSize="@dimen/_16dp"
            app:iconStartPadding="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/vw_divider"
            android:layout_width="1dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_8dp"
            android:background="@color/black_10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/chip_all_categories"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/cg_payment_categories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/vw_divider"
            app:layout_constraintTop_toTopOf="parent"
            app:singleSelection="true" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</HorizontalScrollView>