<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/yellow_5">

    <TextView
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_14dp"
        android:text="@string/change_in_capital_price"
        android:textColor="@color/yellow_60"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_catalog"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_catalog"
        style="@style/ButtonFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_14dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/catalog"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="@dimen/text_14sp"
        android:textStyle="bold"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />
</androidx.constraintlayout.widget.ConstraintLayout>