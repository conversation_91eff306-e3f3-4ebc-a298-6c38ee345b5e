<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_account_confirmation_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragmentContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_account_type_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/tb_account_type"
            layout="@layout/widget_toolbar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_choose_account_header"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="20dp"
            android:text="@string/select_account_type"
            android:textColor="@color/black_40"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_account_type" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_saving"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:drawablePadding="10dp"
            android:gravity="center_vertical"
            android:text="@string/savings_account"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableStartCompat="@drawable/ic_saving"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_choose_account_header"
            app:layout_constraintTop_toBottomOf="@+id/tv_choose_account_header" />

        <include
            android:id="@+id/vw_divider_address"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            app:layout_constraintEnd_toEndOf="@id/tv_saving"
            app:layout_constraintStart_toStartOf="@id/tv_saving"
            app:layout_constraintTop_toBottomOf="@id/tv_saving" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_current"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:drawablePadding="10dp"
            android:gravity="center_vertical"
            android:text="@string/checking_account"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableStartCompat="@drawable/ic_current"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@id/tv_choose_account_header"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_address" />

        <include
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="16dp"
            app:layout_constraintEnd_toEndOf="@id/tv_current"
            app:layout_constraintStart_toStartOf="@id/tv_current"
            app:layout_constraintTop_toBottomOf="@id/tv_current" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>