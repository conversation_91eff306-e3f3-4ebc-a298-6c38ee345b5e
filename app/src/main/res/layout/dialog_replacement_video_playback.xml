<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/replacement_dialog_background_rounded"
    android:padding="16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_dialog_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Video Preview"
            android:textColor="@color/black_800"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_close"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:srcCompat="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/black_60" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/cv_video_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_layout">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <VideoView
                android:id="@+id/video_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center" />

            <!-- Custom Video Controls Overlay -->
            <LinearLayout
                android:id="@+id/ll_video_controls"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/gradient_overlay_bottom"
                android:orientation="horizontal"
                android:padding="12dp"
                android:visibility="gone"
                android:gravity="center_vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_play_pause"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?selectableItemBackgroundBorderless"
                    android:padding="4dp"
                    app:srcCompat="@drawable/replacement_ic_play"
                    app:tint="@android:color/white" />

                <TextView
                    android:id="@+id/tv_current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

                <SeekBar
                    android:id="@+id/seek_bar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="8dp"
                    android:max="100"
                    android:progress="0" />

                <TextView
                    android:id="@+id/tv_total_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="00:00"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_play_button"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_gravity="center"
                app:srcCompat="@drawable/replacement_ic_play"
                 />

            <ProgressBar
                android:id="@+id/pb_loading"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
