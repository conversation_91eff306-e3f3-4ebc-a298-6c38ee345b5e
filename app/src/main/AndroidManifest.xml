<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.bukuwarung.edc">

    <uses-permission android:name="com.pax.permission.ICC"/>
    <uses-permission android:name="com.pax.permission.PICC"/>
    <uses-permission android:name="com.pax.permission.MAGCARD"/>
    <uses-permission android:name="com.pax.permission.PRINTER"/>
    <uses-permission android:name="com.pax.permission.PED"/>

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-permission android:name="com.market.android.app.API_LOCATION" />

    <uses-permission android:name="android.permission.BEEPER"/>
    <uses-permission android:name="android.permission.LED"/>
    <uses-permission android:name="android.permission.SERIALPORT"/>
    <uses-permission android:name="android.permission.SCANNER"/>
    <uses-permission android:name="android.permission.MAGCARDREADER"/>
    <uses-permission android:name="android.permission.INSERTCARDREADER"/>
    <uses-permission android:name="android.permission.PINPAD"/>
    <uses-permission android:name="android.permission.RFCARDREADER"/>
    <uses-permission android:name="android.permission.PRINTER"/>
    <uses-permission android:name="android.permission.PBOC"/>
    <uses-permission android:name="android.permission.DEVICEINFO"/>

    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 往SDCard写入数据权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <!--  从SDCard读取数据权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>

    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <application
        android:name="com.bukuwarung.edc.global.EdcApplication"
        android:allowBackup="false"
        tools:replace="android:allowBackup"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:enableOnBackInvokedCallback="true"
        android:extractNativeLibs="true"
        tools:targetApi="31">

        <activity
            android:name="com.bukuwarung.edc.SplashActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Splash"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="com.bukuwarung.edc.login.ui.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.onboarding.ui.WelcomeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.verifyotp.ui.VerifyOtpActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"
            android:windowSoftInputMode="adjustResize"/>

        <activity android:name="com.bukuwarung.edc.webview.WebviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity
            android:name="com.bukuwarung.edc.homepage.ui.home.HomePageActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="launch"
                    android:scheme="bukuagen" />
            </intent-filter>
        </activity>

        <activity android:name="com.bukuwarung.edc.homepage.ui.RouterActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.homepage.ui.BTRouterActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.card.ui.transactiondetail.ui.TransactionDetailsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.core.PaymentInputActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.category.PaymentCategoryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.core.PaymentConfirmationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.core.PaymentStatusActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity
            android:name="com.bukuwarung.edc.payments.ui.core.NewPaymentPinActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity
            android:name="com.bukuwarung.edc.payments.ui.core.PaymentPinFixedTerminalActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.card.transfermoney.ui.MoneyTransferConfirmationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.transfermoney.ui.AddBankAccountMoneyTransferActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.payments.ui.core.OrderDetailActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.saldo.SaldoTopupActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.payments.ui.history.OrderHistoryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.settings.ui.setting.SettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.settings.ui.setting.BluetoothDiscoveryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.settings.ui.profile.ProfileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.bluetooth_printer.activities.print.setup.BluetoothDeviceScanActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.bluetooth_printer.activities.print.setup.FirmwareUpgradeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ui.CardReaderInstructionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ui.BTCardReaderInstructionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity
            android:name="com.bukuwarung.edc.homepage.ui.history.HistoryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar" />

        <activity android:name="com.bukuwarung.edc.card.ui.ChooseAccountTypeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ui.CardInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ui.checkbalance.BalanceInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ui.receipt.CardReceiptActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.CardPinDynamicActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.ExternalPinpadActivity"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.transfermoney.ui.ConfirmTransferActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".card.CardPinFirebaseDynamicActivity"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.card.cardhistory.ui.CardTransactionHistoryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.ewallet.view.EwalletBillersActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.baseactivity.view.PpobActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.confirmation.view.PpobOrderFormActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.confirmation.view.PpobStatusActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.train.view.TrainTicketDetailsActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".ppob.train.view.TrainTicketWebviewActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name="com.bukuwarung.edc.payments.ui.contacts.PaymentContactActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity android:name=".settings.ui.setting.SoundSettingsActivity"
            android:theme="@style/Theme.Material3.Dark.NoActionBar"/>

        <activity
            android:name=".card.ui.edcdevices.ui.DeviceListActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name=".card.cashwithdrawal.ui.activity.AddSettlementBankAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name=".card.cashwithdrawal.ui.activity.SettlementAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity android:name=".order.orderdetail.ui.EdcOrderDetailsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name="com.bukuwarung.edc.settings.ui.setting.ManageSpaceActivity"
            android:screenOrientation="portrait"
            android:label="Manage Space"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MANAGE_PACKAGE_STORAGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.bukuwarung.lib.webview.camera.CameraKycActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name="com.bukuwarung.lib.webview.camera.CameraKycV2Activity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name="com.bukuwarung.lib.webview.SimpleWebViewActivity"
            android:configChanges="locale"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar"
            android:exported="false"
            tools:replace="android:theme" />

        <activity
            android:name="com.bukuwarung.lib.webview.camera.VideoKycActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name="com.bukuwarung.lib.webview.kyc.VideoKycPreviewActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />

        <activity
            android:name="com.bukuwarung.edc.replacement.presentation.form.ReplacementFormActivity"
            android:exported="false"
            android:theme="@style/Theme.Material3.DayNight.NoActionBar" />





        <service android:name="com.bukuwarung.edc.global.service.DownloadParamService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.sdk.service.ACTION_TO_DOWNLOAD_PARAMS"/>
                <category android:name="${applicationId}"/>
            </intent-filter>
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="android:resource"/>
        </provider>

        <service
            android:name=".global.messaging.BukuMessageListener"
            android:exported="false"
            android:enabled="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notif" />
    </application>

</manifest>